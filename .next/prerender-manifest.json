{"version": 4, "routes": {"/analytics": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/analytics", "dataRoute": "/analytics.rsc"}, "/reports": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/reports", "dataRoute": "/reports.rsc"}, "/settings": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/settings", "dataRoute": "/settings.rsc"}, "/watchlist": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/watchlist", "dataRoute": "/watchlist.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/search": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/search", "dataRoute": "/search.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "52bc6c2ceb5ff84176e90690b3bfb8ec", "previewModeSigningKey": "c846ca1214fd2cea3321815bc89790cba6cc4f5eb64e8cb482957bdf54d473f2", "previewModeEncryptionKey": "187fc6ba4765091992232d4dcefd961228cab1ce4bd146ac3b4d025cb7d648c3"}}