# 🚀 Full Stack Startup Guide

## 📋 Overview

This guide provides multiple ways to start your Vacant Land Search platform with both the Go backend and Next.js frontend running together.

## 🛠️ Prerequisites

### Required Software
- **Go 1.22.3+** - Backend API server
- **Node.js 18+** - Frontend development
- **npm** - Package management

### Environment Setup
```bash
# Check versions
go version          # Should be 1.22.3+
node --version      # Should be 18+
npm --version       # Should be 8+
```

## 🎯 Startup Options

### 1. 🚀 **Full Production Stack** (Recommended)
Starts both services in production mode with full monitoring.

```bash
./start-full-stack.sh
```

**Features:**
- ✅ Builds Go backend binary
- ✅ Builds Next.js for production
- ✅ Health checks and monitoring
- ✅ Process management with PIDs
- ✅ Graceful shutdown handling
- ✅ Comprehensive logging

**URLs:**
- Frontend: http://localhost:3000
- Backend: http://localhost:8080

### 2. ⚡ **Quick Development** (Fast Testing)
Starts backend in production mode, frontend in development mode.

```bash
./start-dev.sh
```

**Features:**
- ✅ Quick startup for testing
- ✅ Frontend hot reload
- ✅ Automatic process cleanup
- ✅ Simplified monitoring

**URLs:**
- Frontend: http://localhost:3000 (dev mode)
- Backend: http://localhost:8080

### 3. 🔧 **Manual Startup** (Advanced Users)

#### Start Backend First:
```bash
# Set environment variables
export PORT=8080
export DATABASE_URL="postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require"

# Optional: Set proxy URLs
export PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002"

# Build and run
go mod tidy
go build -o propbolt
./propbolt
```

#### Start Frontend (New Terminal):
```bash
# Development mode
npm run dev

# OR Production mode
npm run build
npm start
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env or export)
```bash
PORT=8080
DATABASE_URL="postgresql://wonderland_owner:<EMAIL>/wonderland?sslmode=require"
PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002"
```

#### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_BASE_URL=https://gold-braid-458901-v2.uc.r.appspot.com
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg
NEXT_PUBLIC_REAL_ESTATE_API_KEY=AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
```

### Port Configuration
- **Backend**: Port 8080 (configurable via PORT env var)
- **Frontend**: Port 3000 (Next.js default)

## 🌐 Service URLs

### Frontend (Next.js)
- **Dashboard**: http://localhost:3000
- **Land Search**: http://localhost:3000/search
- **Watchlist**: http://localhost:3000/watchlist
- **Analytics**: http://localhost:3000/analytics
- **Reports**: http://localhost:3000/reports
- **Settings**: http://localhost:3000/settings

### Backend (Go API)
- **Health Check**: http://localhost:8080
- **API Status**: http://localhost:8080/status
- **Property Search**: http://localhost:8080/api/search
- **Dashboard Stats**: http://localhost:8080/api/dashboard/stats
- **Sync Properties**: http://localhost:8080/api/sync-properties

## 🔍 Health Checks

### Backend Health
```bash
curl http://localhost:8080/status
```

### Frontend Health
```bash
curl http://localhost:3000
```

### Full System Test
```bash
# Test API connectivity
curl -X POST http://localhost:8080/api/search \
  -H "Content-Type: application/json" \
  -d '{"query":"","filters":{}}'

# Test frontend API integration
curl http://localhost:3000/api/dashboard/stats
```

## 🛑 Stopping Services

### Automatic (Recommended)
Press `Ctrl+C` in the terminal running the startup script. This will:
- Gracefully stop both services
- Clean up process files
- Kill any remaining processes on the ports

### Manual Cleanup
```bash
# Kill processes on specific ports
lsof -ti:8080 | xargs kill -9  # Backend
lsof -ti:3000 | xargs kill -9  # Frontend

# Remove PID files
rm -f .backend.pid .frontend.pid
```

## 🐛 Troubleshooting

### Common Issues

#### 1. **Port Already in Use**
```bash
# Check what's using the port
lsof -i :8080
lsof -i :3000

# Kill the process
kill -9 <PID>
```

#### 2. **Backend Build Fails**
```bash
# Clean and rebuild
go clean
go mod tidy
go build -o propbolt
```

#### 3. **Frontend Build Fails**
```bash
# Clean and reinstall
rm -rf node_modules .next
npm install
npm run build
```

#### 4. **Database Connection Issues**
- Verify DATABASE_URL is correct
- Check network connectivity to Neon
- Ensure database credentials are valid

#### 5. **API Integration Issues**
- Verify backend is running on port 8080
- Check CORS settings if needed
- Ensure API endpoints are accessible

### Debug Mode

#### Backend Debug
```bash
# Run with verbose logging
go run main.go
```

#### Frontend Debug
```bash
# Run with debug output
DEBUG=* npm run dev
```

## 📊 Monitoring

### Process Monitoring
```bash
# Check if services are running
ps aux | grep propbolt    # Backend
ps aux | grep next        # Frontend

# Check port usage
netstat -tulpn | grep :8080
netstat -tulpn | grep :3000
```

### Log Monitoring
```bash
# Backend logs (if configured)
tail -f backend.log

# Frontend logs
# Check terminal output where npm run dev is running
```

## 🎯 Quick Commands Reference

```bash
# Full production startup
./start-full-stack.sh

# Quick development startup
./start-dev.sh

# Manual backend only
export PORT=8080 && go run main.go

# Manual frontend only
npm run dev

# Build everything
go build -o propbolt && npm run build

# Clean everything
go clean && rm -rf node_modules .next

# Health check
curl http://localhost:8080/status && curl http://localhost:3000
```

## 🎉 Success Indicators

When everything is working correctly, you should see:

1. ✅ **Backend**: "Server started at port 8080" message
2. ✅ **Frontend**: "Ready in XXXms" message from Next.js
3. ✅ **Health**: Both URLs respond to curl requests
4. ✅ **Integration**: Frontend can fetch data from backend APIs
5. ✅ **Database**: Property data loads in the dashboard
6. ✅ **Mapping**: Mapbox displays with property markers

Your Daytona Beach vacant land search platform is now ready for use! 🏖️
